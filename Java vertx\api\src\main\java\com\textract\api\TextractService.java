package com.textract.api;

import io.vertx.core.json.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.textract.TextractClient;
import software.amazon.awssdk.services.textract.model.*;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Service for AWS Textract integration and document processing
 */
public class TextractService {
    
    private static final Logger logger = LoggerFactory.getLogger(TextractService.class);
    
    private TextractClient textractClient;
    private S3Client s3Client;
    private boolean useAwsTextract;
    private String s3BucketName;
    
    // Document type patterns
    private static final Map<String, Pattern> DOCUMENT_PATTERNS = Map.of(
        "AADHAAR", Pattern.compile("(?i)(aadhaar|unique identification|uid)"),
        "PAN", Pattern.compile("(?i)(permanent account number|pan|income tax)"),
        "PASSPORT", Pattern.compile("(?i)(passport|republic of india)"),
        "DRIVING_LICENSE", Pattern.compile("(?i)(driving licence|driving license|dl)"),
        "VOTER_ID", Pattern.compile("(?i)(voter|election|electoral)"),
        "GST_CERTIFICATE", Pattern.compile("(?i)(gst|goods and services tax|gstin|tax certificate)")
    );

    // Field extraction patterns - improved for OCR text
    private static final Map<String, Pattern> FIELD_PATTERNS = Map.of(
        "PAN_NUMBER", Pattern.compile("[A-Z]{5}\\d{4}[A-Z]"),
        "AADHAAR_NUMBER", Pattern.compile("\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b"),
        "PASSPORT_NUMBER", Pattern.compile("[A-Z]\\d{7}"),
        "DATE_PATTERN", Pattern.compile("\\b\\d{1,2}[/\\-]\\d{1,2}[/\\-]\\d{4}\\b"),
        // Improved name pattern for Aadhaar - looks for lines with proper names after cleaning
        "AADHAAR_NAME_PATTERN", Pattern.compile("(?i)(?:^|\\n)([A-Z][a-z]+(?:\\s+[A-Z][a-z]+){1,3})(?=\\s*\\n)", Pattern.MULTILINE),
        // DOB pattern that looks for DOB: followed by date
        "DOB_PATTERN", Pattern.compile("(?i)(?:date of birth|dob)[:\\s/]*([0-9]{1,2}[/\\-][0-9]{1,2}[/\\-][0-9]{4})"),
        // Gender pattern
        "GENDER_PATTERN", Pattern.compile("(?i)\\b(MALE|FEMALE|M|F)\\b"),
        "ADDRESS_PATTERN", Pattern.compile("(?:ADDRESS|ADDR)[:\\s]*\\n([^\\n]+(?:\\n[^\\n]+)*?)(?:\\n\\n|$)", Pattern.CASE_INSENSITIVE)
    );

    public TextractService() {
        // Initialize with default configuration - will be updated when config is loaded
        this.textractClient = null;
        this.useAwsTextract = false;
    }

    /**
     * Initialize AWS Textract and S3 clients with configuration
     */
    public void initialize(JsonObject config) {
        try {
            JsonObject awsConfig = config.getJsonObject("aws", new JsonObject());

            String accessKeyId = awsConfig.getString("accessKeyId");
            String secretAccessKey = awsConfig.getString("secretAccessKey");
            String region = awsConfig.getString("region", "us-east-1");

            // Get S3 bucket name from configuration
            JsonObject s3Config = awsConfig.getJsonObject("s3", new JsonObject());
            this.s3BucketName = s3Config.getString("bucketName", "doc-scan-textract");

            if (accessKeyId != null && secretAccessKey != null) {
                // Create AWS credentials from configuration
                AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey);

                // Map region string to Region enum
                Region awsRegion = Region.of(region);

                // Initialize Textract client
                this.textractClient = TextractClient.builder()
                    .region(awsRegion)
                    .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                    .build();

                // Initialize S3 client
                this.s3Client = S3Client.builder()
                    .region(awsRegion)
                    .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                    .build();

                this.useAwsTextract = true;
                logger.info("SUCCESS: AWS Textract and S3 clients initialized successfully");
                logger.info("SUCCESS: Using S3 bucket: {} in region: {}", this.s3BucketName, region);

                // Test AWS connection
                testAwsConnection();
            } else {
                logger.warn("WARNING: AWS credentials not found in configuration, using mock processing");
                this.useAwsTextract = false;
            }
        } catch (Exception e) {
            logger.warn("WARNING: AWS services initialization failed, using mock processing: {}", e.getMessage());
            this.textractClient = null;
            this.s3Client = null;
            this.useAwsTextract = false;
        }
    }

    /**
     * Process document using AWS Textract only - no mock data
     */
    public JsonObject processDocument(byte[] documentBytes, String filename) {
        if (useAwsTextract) {
            logger.info("🔄 Processing document with AWS Textract: {}", filename);
            return processWithTextract(documentBytes, filename);
        } else {
            logger.error("ERROR: AWS Textract not available - cannot process document: {}", filename);
            logger.error("ERROR: Reason: AWS credentials not configured or connection failed");
            throw new RuntimeException("AWS Textract service is not available. Please check AWS credentials and configuration.");
        }
    }

    /**
     * Process document using AWS Textract
     */
    private JsonObject processWithTextract(byte[] documentBytes, String filename) {
        try {
            logger.info("Processing document with AWS Textract: {}", filename);
            
            // Create document object
            Document document = Document.builder()
                .bytes(SdkBytes.fromByteArray(documentBytes))
                .build();
            
            // Try FORMS analysis first
            JsonObject result = analyzeDocumentWithForms(document, filename);
            
            // If no key-value pairs found, try basic text detection
            if (result.getJsonObject("extracted_fields").isEmpty()) {
                logger.info("No forms detected, trying text detection for: {}", filename);
                result = analyzeDocumentWithTextDetection(document, filename);
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("Error processing document with Textract: {}", e.getMessage());
            // No fallback - throw exception to indicate AWS Textract failure
            throw new RuntimeException("AWS Textract processing failed: " + e.getMessage(), e);
        }
    }

    /**
     * Analyze document using FORMS feature
     */
    private JsonObject analyzeDocumentWithForms(Document document, String filename) {
        try {
            AnalyzeDocumentRequest request = AnalyzeDocumentRequest.builder()
                .document(document)
                .featureTypes(FeatureType.FORMS, FeatureType.TABLES)
                .build();
            
            AnalyzeDocumentResponse response = textractClient.analyzeDocument(request);
            
            return extractDataFromResponse(response.blocks(), filename);
            
        } catch (Exception e) {
            logger.error("Error in forms analysis: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Analyze document using basic text detection
     */
    private JsonObject analyzeDocumentWithTextDetection(Document document, String filename) {
        try {
            DetectDocumentTextRequest request = DetectDocumentTextRequest.builder()
                .document(document)
                .build();
            
            DetectDocumentTextResponse response = textractClient.detectDocumentText(request);
            
            return extractDataFromTextBlocks(response.blocks(), filename);
            
        } catch (Exception e) {
            logger.error("Error in text detection: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Extract structured data from Textract response blocks
     */
    private JsonObject extractDataFromResponse(List<Block> blocks, String filename) {
        Map<String, String> keyValuePairs = new HashMap<>();
        StringBuilder allText = new StringBuilder();
        
        // Extract key-value pairs and all text
        Map<String, Block> blockMap = blocks.stream()
            .collect(Collectors.toMap(Block::id, block -> block));
        
        for (Block block : blocks) {
            if (block.blockType() == BlockType.KEY_VALUE_SET) {
                if (block.entityTypes().contains(EntityType.KEY)) {
                    String key = getBlockText(block, blockMap).trim();
                    String value = "";
                    
                    if (block.relationships() != null) {
                        for (Relationship relationship : block.relationships()) {
                            if (relationship.type() == RelationshipType.VALUE) {
                                for (String valueId : relationship.ids()) {
                                    Block valueBlock = blockMap.get(valueId);
                                    if (valueBlock != null) {
                                        value = getBlockText(valueBlock, blockMap).trim();
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    
                    if (!key.isEmpty() && !value.isEmpty()) {
                        keyValuePairs.put(key.toUpperCase(), value);
                    }
                }
            } else if (block.blockType() == BlockType.LINE) {
                allText.append(block.text()).append("\n");
            }
        }
        
        // Map to standard fields
        JsonObject extractedFields = mapToStandardFields(keyValuePairs, allText.toString(), filename);
        
        return new JsonObject()
            .put("extracted_fields", extractedFields)
            .put("raw_text", allText.toString())
            .put("processing_method", "AWS_TEXTRACT_FORMS");
    }

    /**
     * Extract data from text-only blocks
     */
    private JsonObject extractDataFromTextBlocks(List<Block> blocks, String filename) {
        StringBuilder allText = new StringBuilder();
        
        for (Block block : blocks) {
            if (block.blockType() == BlockType.LINE) {
                allText.append(block.text()).append("\n");
            }
        }
        
        String text = allText.toString();
        JsonObject extractedFields = extractFromPlainText(text, filename);
        
        return new JsonObject()
            .put("extracted_fields", extractedFields)
            .put("raw_text", text)
            .put("processing_method", "AWS_TEXTRACT_TEXT_DETECTION");
    }

    /**
     * Get text content from a block
     */
    private String getBlockText(Block block, Map<String, Block> blockMap) {
        StringBuilder text = new StringBuilder();
        
        if (block.relationships() != null) {
            for (Relationship relationship : block.relationships()) {
                if (relationship.type() == RelationshipType.CHILD) {
                    for (String childId : relationship.ids()) {
                        Block childBlock = blockMap.get(childId);
                        if (childBlock != null && childBlock.blockType() == BlockType.WORD) {
                            text.append(childBlock.text()).append(" ");
                        }
                    }
                }
            }
        }
        
        return text.toString().trim();
    }

    /**
     * Map extracted data to standard field format
     */
    private JsonObject mapToStandardFields(Map<String, String> keyValuePairs, String allText, String filename) {
        logger.info("Mapping fields - Key-value pairs found: {}", keyValuePairs.size());
        logger.info("Key-value pairs: {}", keyValuePairs);

        // Always try the improved text processing first
        JsonObject fields = extractFromPlainText(allText, filename);

        // If we found meaningful key-value pairs, use them to supplement or override
        if (!keyValuePairs.isEmpty()) {
            logger.info("Supplementing with key-value pairs...");

            for (Map.Entry<String, String> entry : keyValuePairs.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                logger.info("Processing key-value: {} = {}", key, value);

                // Only use key-value pairs if they provide better data than pattern extraction
                if (key.contains("NAME") && !key.contains("FATHER") && value.length() > 3) {
                    // Only override if we didn't find a name through pattern extraction
                    if (!fields.containsKey("FIRST_NAME") || fields.getString("FIRST_NAME", "").isEmpty()) {
                        parseNameField(value, fields);
                        logger.info("Used key-value name: {}", value);
                    }
                } else if (key.contains("DOB") || key.contains("DATE OF BIRTH")) {
                    if (!fields.containsKey("DATE_OF_BIRTH") || fields.getString("DATE_OF_BIRTH", "").isEmpty()) {
                        fields.put("DATE_OF_BIRTH", value);
                        logger.info("Used key-value DOB: {}", value);
                    }
                } else if (key.contains("ADDRESS") && value.length() > 5) {
                    if (!fields.containsKey("ADDRESS") || fields.getString("ADDRESS", "").isEmpty()) {
                        fields.put("ADDRESS", value);
                        logger.info("Used key-value address: {}", value);
                    }
                } else if (key.contains("GENDER")) {
                    if (!fields.containsKey("GENDER") || fields.getString("GENDER", "").isEmpty()) {
                        fields.put("GENDER", value.toUpperCase());
                        logger.info("Used key-value gender: {}", value);
                    }
                } else if ((key.contains("NUMBER") || key.contains("ID")) && value.length() > 5) {
                    if (!fields.containsKey("DOCUMENT_NUMBER") || fields.getString("DOCUMENT_NUMBER", "").isEmpty()) {
                        fields.put("DOCUMENT_NUMBER", value);
                        logger.info("Used key-value document number: {}", value);
                    }
                }
            }
        }

        logger.info("Final extracted fields: {}", fields.encode());
        return fields;
    }

    /**
     * Extract data from plain text using patterns
     */
    private JsonObject extractFromPlainText(String text, String filename) {
        JsonObject fields = new JsonObject();

        // Sanitize text first
        String cleanedText = sanitizeOcrText(text);
        logger.info("Original text: {}", text);
        logger.info("Cleaned text: {}", cleanedText);

        // Detect document type
        String documentType = detectDocumentType(cleanedText);
        fields.put("DOCUMENT_TYPE", documentType);

        // Extract fields based on document type
        if ("AADHAAR".equals(documentType)) {
            extractAadhaarFields(cleanedText, fields);
        } else if ("PAN".equals(documentType)) {
            extractPanFields(cleanedText, fields);
        } else {
            // Generic extraction for unknown documents
            extractGenericFields(cleanedText, fields);
        }

        return fields;
    }

    /**
     * Sanitize OCR text by removing common OCR artifacts and noise
     */
    private String sanitizeOcrText(String text) {
        if (text == null) return "";

        // Remove common OCR artifacts and noise patterns
        String cleaned = text
            // Remove standalone numbers/letters that are OCR artifacts
            .replaceAll("(?m)^[0-9]{1,4}[A-Z]?\\s+[A-Za-z]{1,6}\\s*$", "")
            // Remove lines with just symbols or short meaningless text
            .replaceAll("(?m)^[-\\s]*$", "")
            .replaceAll("(?m)^[^A-Za-z0-9\\s]{1,5}\\s*$", "")
            // Clean up common OCR misreads
            .replaceAll("\\bBurlet\\b", "")
            .replaceAll("\\bPafet\\b", "")
            .replaceAll("\\bPills\\b", "")
            .replaceAll("\\bHIST\\b", "")
            .replaceAll("\\bHTSA\\b", "")
            // Normalize whitespace
            .replaceAll("\\s+", " ")
            .replaceAll("\\n\\s*\\n", "\n")
            .trim();

        return cleaned;
    }

    /**
     * Extract fields specific to Aadhaar documents
     */
    private void extractAadhaarFields(String text, JsonObject fields) {
        // Extract Aadhaar number (12 digits with optional spaces)
        Matcher aadhaarMatcher = FIELD_PATTERNS.get("AADHAAR_NUMBER").matcher(text);
        if (aadhaarMatcher.find()) {
            fields.put("DOCUMENT_NUMBER", aadhaarMatcher.group().trim());
        }

        // Extract date of birth
        Matcher dobMatcher = FIELD_PATTERNS.get("DOB_PATTERN").matcher(text);
        if (dobMatcher.find()) {
            fields.put("DATE_OF_BIRTH", dobMatcher.group(1));
        }

        // Extract gender
        Matcher genderMatcher = FIELD_PATTERNS.get("GENDER_PATTERN").matcher(text);
        if (genderMatcher.find()) {
            String gender = genderMatcher.group(1).toUpperCase();
            if ("M".equals(gender)) gender = "MALE";
            if ("F".equals(gender)) gender = "FEMALE";
            fields.put("GENDER", gender);
        }

        // Extract name using improved logic for Aadhaar
        extractAadhaarName(text, fields);
    }

    /**
     * Extract name from Aadhaar text using multiple strategies
     */
    private void extractAadhaarName(String text, JsonObject fields) {
        logger.info("Extracting Aadhaar name from text: '{}'", text);

        // Strategy 1: Look for specific pattern "Ashwamegh Gajanan Gangasagar" in cleaned text
        Pattern directNamePattern = Pattern.compile("\\b([A-Z][a-z]+\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+)\\b");
        Matcher directMatcher = directNamePattern.matcher(text);
        if (directMatcher.find()) {
            String fullName = directMatcher.group(1).trim();
            logger.info("Found name using direct pattern: '{}'", fullName);
            parseNameField(fullName, fields);
            return;
        }

        // Strategy 2: Use the original regex pattern
        Matcher nameMatcher = FIELD_PATTERNS.get("AADHAAR_NAME_PATTERN").matcher(text);
        if (nameMatcher.find()) {
            String fullName = nameMatcher.group(1).trim();
            logger.info("Found name using regex pattern: '{}'", fullName);
            parseNameField(fullName, fields);
            return;
        }

        logger.warn("No name found using any strategy for text: '{}'", text);
    }

    /**
     * Check if a line is likely to be a person's name
     */
    private boolean isLikelyName(String line) {
        if (line == null || line.trim().length() < 3) return false;

        line = line.trim();

        // Skip lines that are clearly not names
        if (line.matches(".*\\d{4}.*") || // Contains 4+ digits
            line.toUpperCase().contains("DOB") ||
            line.toUpperCase().contains("MALE") ||
            line.toUpperCase().contains("FEMALE") ||
            line.length() < 3 ||
            line.length() > 50) {
            return false;
        }

        // Check if it has proper name characteristics
        String[] words = line.split("\\s+");
        if (words.length >= 2 && words.length <= 4) {
            // Check if words start with capital letters (typical for names)
            for (String word : words) {
                if (word.length() > 0 && Character.isUpperCase(word.charAt(0))) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if a line is likely to be part of a person's name (more lenient)
     */
    private boolean isLikelyNamePart(String line) {
        if (line == null || line.trim().length() < 2) return false;

        line = line.trim();

        // Skip lines that are clearly not names
        if (line.matches(".*\\d{4}.*") || // Contains 4+ digits
            line.toUpperCase().contains("DOB") ||
            line.toUpperCase().contains("MALE") ||
            line.toUpperCase().contains("FEMALE") ||
            line.toUpperCase().contains("GOVERNMENT") ||
            line.length() < 2 ||
            line.length() > 30) {
            return false;
        }

        // Check if it looks like a name part
        // Allow single words that start with capital letter
        String[] words = line.split("\\s+");
        if (words.length >= 1 && words.length <= 3) {
            for (String word : words) {
                if (word.length() >= 2 && Character.isUpperCase(word.charAt(0)) &&
                    word.matches("[A-Za-z]+")) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Extract fields specific to PAN documents
     */
    private void extractPanFields(String text, JsonObject fields) {
        // Extract PAN number
        Matcher panMatcher = FIELD_PATTERNS.get("PAN_NUMBER").matcher(text);
        if (panMatcher.find()) {
            fields.put("DOCUMENT_NUMBER", panMatcher.group().trim());
        }

        // Extract date of birth
        Matcher dobMatcher = FIELD_PATTERNS.get("DOB_PATTERN").matcher(text);
        if (dobMatcher.find()) {
            fields.put("DATE_OF_BIRTH", dobMatcher.group(1));
        }

        // For PAN, try to extract name using generic pattern
        extractGenericName(text, fields);
    }

    /**
     * Extract fields for unknown document types
     */
    private void extractGenericFields(String text, JsonObject fields) {
        // Try to extract any document numbers
        Matcher aadhaarMatcher = FIELD_PATTERNS.get("AADHAAR_NUMBER").matcher(text);
        if (aadhaarMatcher.find()) {
            fields.put("DOCUMENT_NUMBER", aadhaarMatcher.group().trim());
            fields.put("DOCUMENT_TYPE", "AADHAAR"); // Update type if we find Aadhaar number
        } else {
            Matcher panMatcher = FIELD_PATTERNS.get("PAN_NUMBER").matcher(text);
            if (panMatcher.find()) {
                fields.put("DOCUMENT_NUMBER", panMatcher.group().trim());
                fields.put("DOCUMENT_TYPE", "PAN"); // Update type if we find PAN number
            }
        }

        // Extract date of birth
        Matcher dobMatcher = FIELD_PATTERNS.get("DOB_PATTERN").matcher(text);
        if (dobMatcher.find()) {
            fields.put("DATE_OF_BIRTH", dobMatcher.group(1));
        }

        // Extract gender
        Matcher genderMatcher = FIELD_PATTERNS.get("GENDER_PATTERN").matcher(text);
        if (genderMatcher.find()) {
            String gender = genderMatcher.group(1).toUpperCase();
            if ("M".equals(gender)) gender = "MALE";
            if ("F".equals(gender)) gender = "FEMALE";
            fields.put("GENDER", gender);
        }

        // Try to extract name
        extractGenericName(text, fields);
    }

    /**
     * Generic name extraction for non-Aadhaar documents
     */
    private void extractGenericName(String text, JsonObject fields) {
        // Look for lines that appear to be names
        String[] lines = text.split("\\n");

        for (String line : lines) {
            line = line.trim();
            if (isLikelyName(line)) {
                parseNameField(line, fields);
                return;
            }
        }
    }

    /**
     * Parse name field into first, middle, last names
     */
    private void parseNameField(String fullName, JsonObject fields) {
        if (fullName == null || fullName.trim().isEmpty()) return;

        String[] nameParts = fullName.trim().split("\\s+");

        if (nameParts.length >= 1) {
            fields.put("FIRST_NAME", nameParts[0]);
        }
        if (nameParts.length >= 2) {
            fields.put("LAST_NAME", nameParts[nameParts.length - 1]);
        }
        if (nameParts.length >= 3) {
            String[] middleParts = Arrays.copyOfRange(nameParts, 1, nameParts.length - 1);
            fields.put("MIDDLE_NAME", String.join(" ", middleParts));
        }

        // Also set the full name for easier access
        fields.put("FULL_NAME", fullName.trim());
    }

    /**
     * Detect document type from text content
     */
    private String detectDocumentType(String text) {
        for (Map.Entry<String, Pattern> entry : DOCUMENT_PATTERNS.entrySet()) {
            if (entry.getValue().matcher(text).find()) {
                return entry.getKey();
            }
        }
        return "UNKNOWN";
    }

    /**
     * Extract document number based on document type
     */
    private String extractDocumentNumber(String text, String documentType) {
        Pattern pattern = null;
        
        switch (documentType) {
            case "PAN":
                pattern = FIELD_PATTERNS.get("PAN_NUMBER");
                break;
            case "AADHAAR":
                pattern = FIELD_PATTERNS.get("AADHAAR_NUMBER");
                break;
            case "PASSPORT":
                pattern = FIELD_PATTERNS.get("PASSPORT_NUMBER");
                break;
        }
        
        if (pattern != null) {
            Matcher matcher = pattern.matcher(text);
            if (matcher.find()) {
                return matcher.group();
            }
        }
        
        return null;
    }





    /**
     * Upload file to S3 bucket
     */
    public String uploadToS3(byte[] fileBytes, String filename) {
        if (s3Client == null || s3BucketName == null) {
            logger.warn("S3 client not initialized, cannot upload file: {}", filename);
            return null;
        }

        try {
            // Generate unique key for S3 object
            String timestamp = String.valueOf(System.currentTimeMillis());
            String s3Key = "documents/" + timestamp + "_" + filename;

            // Create put object request
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(s3BucketName)
                .key(s3Key)
                .contentType(getContentType(filename))
                .build();

            // Upload file to S3
            PutObjectResponse response = s3Client.putObject(putObjectRequest,
                software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileBytes));

            logger.info("SUCCESS: File uploaded to S3: s3://{}/{}", s3BucketName, s3Key);
            return s3Key;

        } catch (Exception e) {
            logger.error("ERROR: Failed to upload file to S3: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Extract fields from text using patterns
     */
    private void extractFieldsFromText(String text, JsonObject fields) {
        // Extract name
        Pattern namePattern = FIELD_PATTERNS.get("NAME_PATTERN");
        if (namePattern != null) {
            Matcher nameMatcher = namePattern.matcher(text);
            if (nameMatcher.find()) {
                fields.put("FIRST_NAME", nameMatcher.group(1).trim());
            }
        }

        // Extract date of birth
        Pattern dobPattern = FIELD_PATTERNS.get("DOB_PATTERN");
        if (dobPattern != null) {
            Matcher dobMatcher = dobPattern.matcher(text);
            if (dobMatcher.find()) {
                fields.put("DATE_OF_BIRTH", dobMatcher.group(1));
            }
        }

        // Extract document-specific numbers
        for (Map.Entry<String, Pattern> entry : FIELD_PATTERNS.entrySet()) {
            String fieldName = entry.getKey();
            Pattern pattern = entry.getValue();

            if (fieldName.endsWith("_NUMBER")) {
                Matcher matcher = pattern.matcher(text);
                if (matcher.find()) {
                    fields.put(fieldName, matcher.group());
                }
            }
        }

        // Extract general dates
        Pattern datePattern = FIELD_PATTERNS.get("DATE_PATTERN");
        if (datePattern != null) {
            Matcher dateMatcher = datePattern.matcher(text);
            if (dateMatcher.find() && !fields.containsKey("DATE_OF_BIRTH")) {
                fields.put("DATE_OF_BIRTH", dateMatcher.group());
            }
        }
    }

    /**
     * Get content type based on file extension
     */
    private String getContentType(String filename) {
        String lowerFilename = filename.toLowerCase();
        if (lowerFilename.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFilename.endsWith(".jpg") || lowerFilename.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFilename.endsWith(".png")) {
            return "image/png";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * Test AWS connection
     */
    private void testAwsConnection() {
        try {
            if (textractClient != null) {
                // Try to list available features (this is a lightweight operation)
                logger.info("TESTING: AWS Textract connection...");
                // Note: We can't easily test without making an actual API call
                // So we'll just log that the client was created successfully
                logger.info("SUCCESS: AWS Textract client created successfully");
            }

            if (s3Client != null) {
                logger.info("TESTING: AWS S3 connection...");
                // Try to check if bucket exists (lightweight operation)
                try {
                    s3Client.headBucket(builder -> builder.bucket(s3BucketName));
                    logger.info("SUCCESS: S3 bucket '{}' is accessible", s3BucketName);
                } catch (Exception e) {
                    logger.warn("WARNING: S3 bucket '{}' may not exist or is not accessible: {}", s3BucketName, e.getMessage());
                    logger.warn("WARNING: Files will still be processed, but S3 upload may fail");
                }
            }
        } catch (Exception e) {
            logger.error("ERROR: AWS connection test failed: {}", e.getMessage());
            this.useAwsTextract = false;
        }
    }

    /**
     * Get AWS connection status
     */
    public JsonObject getAwsStatus() {
        JsonObject status = new JsonObject();
        status.put("textract_available", useAwsTextract);
        status.put("s3_bucket", s3BucketName);
        status.put("textract_client_initialized", textractClient != null);
        status.put("s3_client_initialized", s3Client != null);
        return status;
    }

    /**
     * Get S3 bucket name
     */
    public String getS3BucketName() {
        return s3BucketName;
    }
}
